<!DOCTYPE html>

<html lang="zh">

<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>ST计划</title>
    <!-- 添加网站图标 -->
    <link rel="icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <link rel="shortcut icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <!-- 内联PNG图标，用于不支持SVG的浏览器 -->
    <link rel="icon" href="data:image/png;base64,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" type="image/png">
    <!-- 样式文件 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    <link rel="stylesheet" type="text/css" href="css/main.css">
    <!-- 添加解决页面左移问题的样式 -->
    <style>
        body {
            overflow-x: hidden;
        }
        .view-container {
            width: 100%;
            overflow-x: hidden;
        }
        /* 移除卡片的最小高度 */
        .goal-item {
            margin-bottom: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        /* 卡片布局样式 - 紧凑优化版 */
        .goal-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr) !important; /* 强制一行三个卡片 */
            gap: 8px;
        }
        .goal-item {
            width: calc(33.333% - 6px);
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        /* 响应式调整 */
        @media (max-width: 992px) {
            .goal-container {
                grid-template-columns: repeat(2, 1fr) !important; /* 中等屏幕一行两个卡片 */
            }
        }
        @media (max-width: 576px) {
            .goal-container {
                grid-template-columns: 1fr !important; /* 小屏幕一行一个卡片 */
            }
        }
        /* 顶部功能按钮样式 */
        .top-nav-buttons {
            margin-bottom: 10px; /* 减少下边距 */
        }
        /* 密码验证层样式 */
        #password-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.95);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            flex-direction: column;
        }
        .password-container {
            width: 320px;
            max-width: 90%;
            background-color: #fff;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .password-container h3 {
            margin-bottom: 20px;
            color: #333;
        }
        .password-container .form-group {
            margin-bottom: 20px;
        }
        .password-error {
            color: #dc3545;
            margin-top: 10px;
            display: none;
        }

        /* 存款进度界面紧凑优化 */
        .goal-container .card .card-body .d-flex.justify-content-between {
            margin-bottom: 0.2rem !important;
        }

        /* 存款进度按钮金色闪闪发光样式 */
        label[for="savingsMode"] {
            background: linear-gradient(45deg, #FFD700, #FFA500, #FFD700, #FFFF00) !important;
            background-size: 400% 400% !important;
            color: #8B4513 !important;
            font-weight: bold !important;
            text-shadow: 0 0 5px rgba(255, 215, 0, 0.8) !important;
            border: 2px solid #FFD700 !important;
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.6), inset 0 0 15px rgba(255, 255, 255, 0.3) !important;
            animation: goldShine 2s ease-in-out infinite !important;
            position: relative !important;
            overflow: hidden !important;
        }

        label[for="savingsMode"]:hover {
            background: linear-gradient(45deg, #FFFF00, #FFD700, #FFA500, #FFD700) !important;
            box-shadow: 0 0 25px rgba(255, 215, 0, 0.8), inset 0 0 20px rgba(255, 255, 255, 0.4) !important;
            transform: translateY(-2px) !important;
        }

        label[for="savingsMode"]::before {
            content: '' !important;
            position: absolute !important;
            top: -50% !important;
            left: -50% !important;
            width: 200% !important;
            height: 200% !important;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent) !important;
            transform: rotate(45deg) !important;
            animation: goldSweep 3s linear infinite !important;
        }

        @keyframes goldShine {
            0%, 100% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
        }

        @keyframes goldSweep {
            0% {
                transform: translateX(-100%) translateY(-100%) rotate(45deg);
            }
            100% {
                transform: translateX(100%) translateY(100%) rotate(45deg);
            }
        }

        .goal-container .card .card-body .d-flex.gap-2 .btn {
            margin: 0 1px !important;
        }

        /* 进一步减少卡片内部间距 */
        .goal-container .card .card-body {
            padding: 0.4rem 0.5rem 0.3rem 0.5rem !important;
        }

        /* 优化按钮组间距 */
        .goal-container .d-flex.gap-2 {
            gap: 0.2rem !important;
        }


    </style>
</head>

<body>
    <!-- 密码验证层 -->
    <div id="password-overlay" style="display: none">
        <div class="password-container">
            <h3>ST计划访问验证</h3>
            <div class="form-group">
                <input type="password" id="access-password" class="form-control" placeholder="请输入访问密码">
            </div>
            <button id="password-submit" class="btn btn-primary w-100">确认</button>
            <div id="password-error" class="password-error">密码错误，请重试</div>
            <div class="form-check mt-3">
                <input class="form-check-input" type="checkbox" id="remember-password">
                <label class="form-check-label" for="remember-password">
                    在此设备上记住我
                </label>
            </div>
        </div>
    </div>

    <div class="container mt-3"> <!-- 减少顶部边距 -->
        <!-- 将功能按钮移到顶部并居中 -->
        <div class="row mb-3"> <!-- 减少下边距 -->
            <div class="col-12 d-flex justify-content-center">
                <div class="btn-group flex-wrap top-nav-buttons" role="group">
                    <input class="btn-check" id="savingsMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="savingsMode">存款进度</label>
                    <input class="btn-check" id="moneyTasksMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="moneyTasksMode" onclick="window.location.href='money-goal-tracker.html'">赚钱任务</label>
                    <input class="btn-check" id="moneyProjectsMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="moneyProjectsMode" onclick="window.location.href='money-projects.html'">赚钱项目</label>
                    <input class="btn-check" id="ageMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="ageMode">时间进度</label>
                    <input class="btn-check" id="timeTrackingMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="timeTrackingMode">时间统计</label>
                    <input class="btn-check" id="visionScenesMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="visionScenesMode" onclick="window.location.href='vision-scenes.html'">记忆树</label>
                    <input class="btn-check" id="dailyTimeMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="dailyTimeMode" onclick="window.location.href='daily-time.html'">时间线</label>
                    <input class="btn-check" id="timeAllocationMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-success btn-sm" for="timeAllocationMode" onclick="window.location.href='time-allocation.html'">24h分配</label>
                    <input class="btn-check" id="priorityMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-warning btn-sm" for="priorityMode" onclick="window.location.href='priority.html'">优先级</label>
                    <input class="btn-check" id="taskProgressMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="taskProgressMode" onclick="window.location.href='task-progress.html'">任务进度</label>
                    <input class="btn-check" id="phaseProgressMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="phaseProgressMode" onclick="window.location.href='phase-progress.html'">阶段进度</label>
                    <input class="btn-check" id="historyMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="historyMode">存取&归档记录</label>
                    <input class="btn-check" id="inspirationMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="inspirationMode">灵感记录</label>
                    <input class="btn-check" id="dietRecordMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="dietRecordMode" onclick="window.location.href='diet-record.html'">饮食记录</label>
                    <input class="btn-check" id="shoppingListMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="shoppingListMode" onclick="window.location.href='shopping-list.html'">购物清单</label>
                    <input class="btn-check" id="resourceCompetitionMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-danger btn-sm" for="resourceCompetitionMode" onclick="window.location.href='resource-practical.html'">资源争夺</label>
                    <input class="btn-check" id="habitMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="habitMode">习惯打卡</label>
                    <input class="btn-check" id="dataProgressMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="dataProgressMode" onclick="window.location.href='data-progress.html'">数据进度</label>
                    <input class="btn-check" id="reviewMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="reviewMode" onclick="window.location.href='review.html'">复盘系统</label>
                    <input class="btn-check" id="lifePathMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="lifePathMode" onclick="window.location.href='life-path.html'">人生路径</label>
                    <input class="btn-check" id="accountManagerMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="accountManagerMode" onclick="window.location.href='account-manager.html'">账号管理</label>
                    <input class="btn-check" id="contactsMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="contactsMode" onclick="window.location.href='contacts.html'">人脉管理</label>
                    <input class="btn-check" id="promptManagerMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="promptManagerMode" onclick="window.location.href='prompt-manager.html'">AI提示词</label>
                    <input class="btn-check" id="scriptManagerMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="scriptManagerMode" onclick="window.location.href='script-manager.html'">脚本管理</label>
                    <input class="btn-check" id="websitesMode" name="viewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="websitesMode">常用网站</label>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-2">
                <div class="card mb-4">
                    <div class="card-header">
                        <!-- 删除设置标题 -->
                    </div>
                    <div class="card-body">
                        <form class="mb-3" id="birthDateForm">
                            <div class="mb-3">
                                <label class="form-label">出生日期 (MMDD):</label>
                                <input class="form-control" id="birthDate" type="text" />
                            </div>
                            <button class="btn btn-primary w-100" type="submit">设置出生日期</button>
                        </form>
                        <form id="goalForm">
                            <div class="mb-3">
                                <label class="form-label">存款内容:</label>
                                <input class="form-control" id="goalName" type="text" />
                            </div>
                            <div class="mb-3">
                                <label class="form-label">目标金额:</label>
                                <input class="form-control" id="goalAmount" type="text" />
                            </div>
                            <button class="btn btn-primary w-100" type="submit">添加存款进度</button>
                        </form>
                        <form id="ageGoalForm" class="mt-3">
                            <div class="mb-3">
                                <label class="form-label">时间目标:</label>
                                <input class="form-control" id="ageGoalName" type="text" />
                            </div>
                            <div class="mb-3">
                                <label class="form-label">目标日期 (MMDD):</label>
                                <input class="form-control" id="ageGoalDate" type="text" />
                            </div>
                            <button class="btn btn-primary w-100" type="submit">添加时间进度</button>
                        </form>
                        <div class="mt-2">
                            <a href="https://wannianrili.bmcx.com/" target="_blank" class="btn btn-outline-primary w-100">
                                <i class="fas fa-calendar-alt"></i> 日历查询
                            </a>
                        </div>
                        <div class="card mt-2">
                            <div class="card-header py-2">记录存取款</div>
                            <div class="card-body p-2">
                                <form id="moneyForm">
                                    <div class="mb-2">
                                        <div class="btn-group w-100" role="group">
                                            <input class="btn-check" id="depositType" name="operationType" type="radio"
                                                value="deposit" />
                                            <label class="btn btn-outline-success" for="depositType">存入</label>
                                            <input class="btn-check" id="withdrawType" name="operationType" type="radio"
                                                value="withdraw" />
                                            <label class="btn btn-outline-warning" for="withdrawType">取出</label>
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <label class="form-label mb-1">金额:</label>
                                        <input class="form-control form-control-sm" id="amount" type="text" />
                                    </div>
                                    <div class="mb-2">
                                        <label class="form-label mb-1">备注:</label>
                                        <input class="form-control form-control-sm" id="note" placeholder="可选"
                                            type="text" />
                                    </div>
                                    <button class="btn btn-primary btn-sm w-100" type="submit">确认</button>
                                </form>
                            </div>
                        </div>
                        <div class="text-center mt-4">
                            <div>总存款</div>
                            <div class="text-success fs-4 fw-bold" id="totalSaved">加载中...</div>
                            <div class="text-muted mt-2">
                                <div id="currentDate"></div>
                                <div id="currentTime"></div>
                            </div>
                            <div class="mt-2">
                                <button id="currencyToggleBtn" class="btn btn-outline-primary btn-sm w-100 mb-2"
                                    onclick="promptExchangeRate()"
                                    title="点击切换货币，按住Ctrl点击设置汇率">
                                    切换金额$
                                </button>
                                <button id="timezoneToggleBtn" class="btn btn-outline-primary btn-sm w-100"
                                    onclick="toggleTimezone()">
                                    切换硅谷时间
                                </button>
                            </div>
                        </div>
                        
                        <!-- 新增数据导入导出容器 -->
                        <div class="card mt-4">
                            <div class="card-header py-2">
                                <i class="fas fa-cloud me-2"></i>数据同步
                            </div>
                            <div class="card-body p-2" id="data-sync-container">
                                <div class="text-center text-muted">
                                    <small><i class="fas fa-spinner fa-spin me-2"></i>云同步功能加载中...</small>
                                </div>
                                <!-- 云同步功能会在此处自动添加 -->
                            </div>
                        </div>

                        <!-- 数据备份功能 -->
                        <div class="card mt-3">
                            <div class="card-header py-2">
                                <i class="fas fa-database me-2"></i>数据备份
                            </div>
                            <div class="card-body p-2">
                                <button id="backup-btn" class="btn btn-outline-secondary btn-sm w-100" onclick="toggleBackupOptions()">
                                    <i class="fas fa-shield-alt me-2"></i>数据备份
                                </button>
                                <div id="backup-options" class="mt-2" style="display: none;">
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-success btn-sm" onclick="exportAllData()">
                                            <i class="fas fa-download me-2"></i>导出数据
                                        </button>
                                        <button class="btn btn-primary btn-sm" onclick="importAllData()">
                                            <i class="fas fa-upload me-2"></i>导入数据
                                        </button>
                                    </div>
                                    <input type="file" id="import-file" accept=".json" style="display: none;" onchange="handleFileImport(event)">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <!-- 已删除导入导出按钮、清空和重置按钮 -->
                    </div>
                    <div class="card-body">
                        <div class="goal-container view-container" id="goalsContainer">
                            <!-- Goals will be added here dynamically -->
                        </div>
                        <!-- 添加时间统计界面 -->
                        <div class="time-tracking-container view-container" style="display: none;">
                            <h3 class="mb-3">时间统计</h3>
                            <button class="btn btn-primary add-event-btn" onclick="addNewEvent()">
                                <i class="fas fa-plus"></i> 添加新事件
                            </button>
                            <div id="events-container">
                                <!-- 事件卡片将在这里动态添加 -->
                            </div>
                        </div>
                        <!-- 添加习惯打卡界面容器 -->
                        <div class="habit-container view-container" style="display: none;">
                            <!-- 习惯打卡内容将在这里动态添加 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.min.js"></script>
    <script src="js/st.js"></script>
    <script src="js/sync.js"></script>
    <script src="js/task-progress.js"></script>
    <script>
        // 添加网站导航按钮点击事件处理
        document.getElementById('websitesMode').addEventListener('change', function() {
            if (this.checked) {
                window.location.href = 'websites.html';
            }
        });
        
        // 添加任务进度按钮点击事件处理
        document.getElementById('taskProgressMode').addEventListener('change', function() {
            if (this.checked) {
                window.location.href = 'task-progress.html';
            }
        });
        
        // 密码验证功能
        document.addEventListener('DOMContentLoaded', function() {
            const passwordOverlay = document.getElementById('password-overlay');
            const passwordInput = document.getElementById('access-password');
            const passwordSubmit = document.getElementById('password-submit');
            const passwordError = document.getElementById('password-error');
            const rememberPassword = document.getElementById('remember-password');
            
            // 默认密码
            const correctPassword = 'st6666';
            
            // 检查是否已经记住密码，只有未验证过时才显示密码验证层
            if (localStorage.getItem('st_authenticated') !== 'true') {
                passwordOverlay.style.display = 'flex';
                
                // 密码提交按钮事件
                passwordSubmit.addEventListener('click', function() {
                    verifyPassword();
                });
                
                // 按回车键也可以提交密码
                passwordInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        verifyPassword();
                    }
                });
            }
            
            // 验证密码函数
            function verifyPassword() {
                const inputPassword = passwordInput.value;
                
                if(inputPassword === correctPassword) {
                    // 密码正确
                    passwordOverlay.style.display = 'none';
                    
                    // 如果选择了记住密码，将验证状态保存到localStorage
                    if(rememberPassword.checked) {
                        localStorage.setItem('st_authenticated', 'true');
                    }
                } else {
                    // 密码错误
                    passwordError.style.display = 'block';
                    passwordInput.value = '';
                    passwordInput.focus();
                    
                    // 震动效果
                    passwordOverlay.classList.add('shake');
                    setTimeout(() => {
                        passwordOverlay.classList.remove('shake');
                    }, 500);
                }
            }
            
            // 添加一个重置密码的功能 (可以在控制台中调用)
            window.resetPassword = function() {
                localStorage.removeItem('st_authenticated');
                alert('密码已重置，下次访问需要重新输入密码');
            };
        });

        // 数据备份功能
        function toggleBackupOptions() {
            const backupOptions = document.getElementById('backup-options');
            const backupBtn = document.getElementById('backup-btn');

            if (backupOptions.style.display === 'none') {
                backupOptions.style.display = 'block';
                backupBtn.innerHTML = '<i class="fas fa-shield-alt me-2"></i>收起备份';
            } else {
                backupOptions.style.display = 'none';
                backupBtn.innerHTML = '<i class="fas fa-shield-alt me-2"></i>数据备份';
            }
        }

        // 导出所有数据
        function exportAllData() {
            try {
                const allData = {};

                // ST项目相关的所有localStorage键名
                const stKeys = [
                    'st_authenticated',
                    'savingsData',
                    'moneyGoals',
                    'currentGoalId',
                    'currentSubgoalId',
                    'websites',
                    'websiteStats',
                    'dailyTimeData',
                    'taskBoards',
                    'tasks',
                    'accountManagerData',
                    'reviewDraft',
                    'visionScenes',
                    'memoryTreeData',
                    'contactsData'
                ];

                // 收集所有ST项目相关数据
                stKeys.forEach(key => {
                    const value = localStorage.getItem(key);
                    if (value !== null) {
                        allData[key] = value;
                    }
                });

                // 也收集所有以st_开头的键
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('st_') && !allData[key]) {
                        allData[key] = localStorage.getItem(key);
                    }
                }

                // 添加导出时间戳和版本信息
                allData.exportTimestamp = new Date().toISOString();
                allData.exportVersion = '2.0';
                allData.exportSource = 'ST计划系统';

                // 创建下载链接
                const dataStr = JSON.stringify(allData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);

                const link = document.createElement('a');
                link.href = url;
                link.download = `ST计划数据备份_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);

                // 计算数据大小并显示
                const dataSize = (dataStr.length / 1024).toFixed(2);
                showNotification(`数据导出成功！文件大小: ${dataSize}KB`, 'success');
            } catch (error) {
                console.error('导出数据失败:', error);
                showNotification('数据导出失败，请重试', 'error');
            }
        }

        // 导入数据
        function importAllData() {
            document.getElementById('import-file').click();
        }

        // 处理文件导入
        function handleFileImport(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const importData = JSON.parse(e.target.result);

                    // 验证数据格式
                    if (!importData.exportVersion) {
                        throw new Error('无效的备份文件格式');
                    }

                    // 计算导入数据大小
                    const dataSize = (JSON.stringify(importData).length / 1024).toFixed(2);

                    // 确认导入
                    const confirmMessage = `导入数据将覆盖当前所有数据，是否继续？\n\n` +
                                         `备份时间：${importData.exportTimestamp ? new Date(importData.exportTimestamp).toLocaleString() : '未知'}\n` +
                                         `数据大小：${dataSize}KB\n` +
                                         `版本：${importData.exportVersion || '未知'}`;

                    if (confirm(confirmMessage)) {

                        // ST项目相关的所有localStorage键名
                        const stKeys = [
                            'st_authenticated',
                            'savingsData',
                            'moneyGoals',
                            'currentGoalId',
                            'currentSubgoalId',
                            'websites',
                            'websiteStats',
                            'dailyTimeData',
                            'taskBoards',
                            'tasks',
                            'accountManagerData',
                            'reviewDraft',
                            'visionScenes',
                            'memoryTreeData',
                            'contactsData'
                        ];

                        // 清除现有ST相关数据
                        const keysToRemove = [];

                        // 清除指定的键
                        stKeys.forEach(key => {
                            if (localStorage.getItem(key) !== null) {
                                keysToRemove.push(key);
                            }
                        });

                        // 清除所有以st_开头的键
                        for (let i = 0; i < localStorage.length; i++) {
                            const key = localStorage.key(i);
                            if (key && key.startsWith('st_')) {
                                keysToRemove.push(key);
                            }
                        }

                        // 执行清除
                        keysToRemove.forEach(key => localStorage.removeItem(key));

                        // 导入新数据
                        let importedCount = 0;
                        Object.keys(importData).forEach(key => {
                            // 跳过元数据字段
                            if (key === 'exportTimestamp' || key === 'exportVersion' || key === 'exportSource') {
                                return;
                            }

                            // 导入数据
                            if (importData[key] !== null && importData[key] !== undefined) {
                                localStorage.setItem(key, importData[key]);
                                importedCount++;
                            }
                        });

                        showNotification(`数据导入成功！已导入 ${importedCount} 项数据，页面将刷新...`, 'success');
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);
                    }
                } catch (error) {
                    console.error('导入数据失败:', error);
                    showNotification('导入数据失败：' + error.message, 'error');
                }
            };
            reader.readAsText(file);

            // 清空文件输入
            event.target.value = '';
        }

        // 显示通知消息
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 10000;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                ${message}
            `;

            document.body.appendChild(notification);

            // 自动移除通知
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, type === 'success' ? 2000 : 5000);
        }
    </script>
</body>

</html>