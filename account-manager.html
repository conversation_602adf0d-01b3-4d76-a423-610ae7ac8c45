<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账号管理系统</title>
    <!-- 添加网站图标 -->
    <link rel="icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <link rel="shortcut icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <!-- 内联PNG图标，用于不支持SVG的浏览器 -->
    <link rel="icon" href="data:image/png;base64,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" type="image/png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="css/account-manager.css">
</head>
<body>
    <!-- 顶部导航 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-user-shield me-2"></i>
                账号管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.html">
                    <i class="fas fa-home me-1"></i>返回主页
                </a>
            </div>
        </div>
    </nav>

    <!-- 主容器 -->
    <div class="container-fluid mt-2">
        <!-- 统计面板 -->
        <div class="row mb-2">
            <div class="col-12">
                <div class="stats-panel">
                    <div class="row">
                        <div class="col-md-3 col-6">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 id="totalAccounts">0</h3>
                                    <p>总账号数</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-6">
                            <div class="stat-card active">
                                <div class="stat-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 id="activeAccounts">0</h3>
                                    <p>活跃账号</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-6">
                            <div class="stat-card pending">
                                <div class="stat-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 id="pendingAccounts">0</h3>
                                    <p>待处理</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-6">
                            <div class="stat-card rented">
                                <div class="stat-icon">
                                    <i class="fas fa-handshake"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 id="rentedAccounts">0</h3>
                                    <p>已租账号</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6 col-12">
                            <div class="stat-card sold">
                                <div class="stat-icon">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 id="soldAccounts">0</h3>
                                    <p>已售账号</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-12">
                            <div class="stat-card expired disabled" id="expiredStatCard">
                                <div class="stat-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 id="expiredAccounts">0</h3>
                                    <p>过期账号 <small class="text-muted">(已隐藏)</small></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作工具栏 -->
        <div class="row mb-2">
            <div class="col-12">
                <div class="toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" onclick="showAddAccountModal()">
                            <i class="fas fa-plus me-1"></i>添加账号
                        </button>
                        <button class="btn btn-secondary" onclick="showBatchOperations()">
                            <i class="fas fa-tasks me-1"></i>批量操作
                        </button>
                        <button class="btn btn-success" onclick="showTypeManagerModal()">
                            <i class="fas fa-cog me-1"></i>管理类型
                        </button>
                        <button class="btn btn-info" onclick="showPlatformManagerModal()">
                            <i class="fas fa-server me-1"></i>管理平台
                        </button>
                    </div>
                    <div class="toolbar-right">
                        <div class="search-box">
                            <input type="text" id="searchInput" class="form-control" placeholder="搜索账号..."
                                   autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false">
                            <i class="fas fa-search search-icon"></i>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="accountManager.clearSearch()" title="清除搜索">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选器 -->
        <div class="row mb-2">
            <div class="col-12">
                <div class="filter-panel">
                    <div class="filter-group">
                        <label>账号类型：</label>
                        <div class="filter-buttons" id="typeFilters">
                            <button class="filter-btn active" data-type="all">全部</button>
                            <!-- 动态生成的类型筛选按钮 -->
                        </div>
                    </div>
                    <div class="filter-group">
                        <label>平台：</label>
                        <div class="filter-buttons" id="platformFilters">
                            <button class="filter-btn active" data-platform="all">全部</button>
                            <!-- 动态生成的平台筛选按钮 -->
                        </div>
                    </div>
                    <div class="filter-group">
                        <label>账号状态：</label>
                        <div class="filter-buttons" id="statusFilters">
                            <button class="filter-btn active" data-status="all">全部</button>
                            <button class="filter-btn" data-status="active">活跃</button>
                            <button class="filter-btn" data-status="pending">待处理</button>
                            <button class="filter-btn" data-status="rented">已租</button>
                            <button class="filter-btn" data-status="sold">已售</button>
                            <button class="filter-btn" data-status="expired">过期</button>
                        </div>
                    </div>
                    <div class="filter-group">
                        <label>显示选项：</label>
                        <div class="filter-options">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="showExpiredAccounts">
                                <label class="form-check-label" for="showExpiredAccounts">
                                    显示过期账号
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 账号列表容器 -->
        <div class="row">
            <div class="col-12">
                <div class="table-responsive">
                    <table class="table table-hover" id="accountsTable">
                        <thead class="table-light">
                            <tr>
                                <th width="5%">
                                    <span><input type="checkbox" id="selectAllAccounts" class="form-check-input"></span>
                                </th>
                                <th width="16%"><span>类型/平台</span></th>
                                <th width="18%"><span>账号信息</span></th>
                                <th width="16%"><span>密码</span></th>
                                <th width="10%"><span>状态</span></th>
                                <th width="8%"><span>价值</span></th>
                                <th width="19%"><span>备注</span></th>
                                <th width="8%"><span>操作</span></th>
                            </tr>
                        </thead>
                        <tbody id="accountsContainer">
                            <!-- 账号行将在这里动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加账号模态框 -->
    <div class="modal fade" id="addAccountModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus-circle me-2"></i>添加新账号
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 隐藏的假输入框来防止自动填充 -->
                    <input type="text" style="display:none" autocomplete="username">
                    <input type="password" style="display:none" autocomplete="current-password">

                    <form id="addAccountForm" autocomplete="off">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">账号类型</label>
                                    <input type="text" class="form-control" id="accountType" placeholder="如：游戏、社交、工具等" required list="typeDatalist" autocomplete="off">
                                    <datalist id="typeDatalist">
                                        <!-- 动态生成的类型选项 -->
                                    </datalist>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">平台名称</label>
                                    <input type="text" class="form-control" id="platform" placeholder="如：Steam、微信等" required list="platformDatalist" autocomplete="off">
                                    <datalist id="platformDatalist">
                                        <!-- 动态生成的平台选项 -->
                                    </datalist>
                                </div>
                            </div>
                        </div>
                        <!-- 账号信息区域 -->
                        <div id="accountInfoSection">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label class="form-label mb-0">账号信息</label>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="accountManager.addAccountRow()">
                                    <i class="fas fa-plus me-1"></i>添加账号
                                </button>
                            </div>

                            <div id="accountRows">
                                <!-- 第一行账号信息 -->
                                <div class="account-row mb-3" data-row="0">
                                    <div class="row align-items-end mb-2">
                                        <div class="col-3">
                                            <label class="form-label">用户名/邮箱</label>
                                            <input type="text" class="form-control form-control-sm username-input" required autocomplete="new-username" name="new-username">
                                        </div>
                                        <div class="col-3">
                                            <label class="form-label">密码</label>
                                            <div class="input-group input-group-sm">
                                                <input type="password" class="form-control password-input" required autocomplete="new-password" name="new-password">
                                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordInRow(this)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="col-2">
                                            <label class="form-label">状态</label>
                                            <select class="form-select form-select-sm status-input">
                                                <option value="active">活跃</option>
                                                <option value="pending">待处理</option>
                                                <option value="rented">已租</option>
                                                <option value="sold">已售</option>
                                                <option value="expired">过期</option>
                                            </select>
                                        </div>
                                        <div class="col-2">
                                            <label class="form-label">价值（元）</label>
                                            <div class="value-range-inputs">
                                                <input type="number" class="form-control form-control-sm value-min-input" min="0" step="0.01" placeholder="最低价">
                                                <span class="value-separator">-</span>
                                                <input type="number" class="form-control form-control-sm value-max-input" min="0" step="0.01" placeholder="最高价">
                                            </div>
                                        </div>
                                        <div class="col-2">
                                            <label class="form-label">&nbsp;</label>
                                            <div>
                                                <button type="button" class="btn btn-outline-danger" onclick="accountManager.removeAccountRow(this)" style="display: none;">
                                                    <i class="fas fa-minus"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-12">
                                            <label class="form-label">备注</label>
                                            <input type="text" class="form-control form-control-sm notes-input" placeholder="可选的备注信息">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <!-- 租赁信息（当状态为已租时显示） -->
                        <div id="addRentInfo" style="display: none;">
                            <hr>
                            <h6><i class="fas fa-handshake me-2"></i>租赁信息</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">租户</label>
                                        <input type="text" class="form-control" id="renter">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">租金（元）</label>
                                        <input type="number" class="form-control" id="rentPrice" min="0" step="0.01">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">开始日期</label>
                                        <input type="date" class="form-control" id="rentStartDate">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">结束日期</label>
                                        <input type="date" class="form-control" id="rentEndDate">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="accountManager.addAccounts()">
                        <span id="addButtonText">添加账号</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑账号模态框 -->
    <div class="modal fade" id="editAccountModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>编辑账号
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 隐藏的假输入框来防止自动填充 -->
                    <input type="text" style="display:none" autocomplete="username">
                    <input type="password" style="display:none" autocomplete="current-password">

                    <form id="editAccountForm" autocomplete="off">
                        <!-- 共享的平台信息 -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">账号类型</label>
                                    <input type="text" class="form-control" id="editAccountType" placeholder="如：游戏、社交、工具等" required list="editTypeDatalist" autocomplete="off">
                                    <datalist id="editTypeDatalist">
                                        <!-- 动态生成的类型选项 -->
                                    </datalist>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">平台名称</label>
                                    <input type="text" class="form-control" id="editPlatform" placeholder="如：Steam、微信等" required list="editPlatformDatalist" autocomplete="off">
                                    <datalist id="editPlatformDatalist">
                                        <!-- 动态生成的平台选项 -->
                                    </datalist>
                                </div>
                            </div>
                        </div>

                        <!-- 账号列表区域 -->
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label class="form-label mb-0">账号信息</label>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="accountManager.addEditAccountRow()">
                                    <i class="fas fa-plus me-1"></i>添加账号
                                </button>
                            </div>
                            <div id="editAccountRows">
                                <!-- 账号行将在这里动态生成 -->
                            </div>
                        </div>

                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="accountManager.updateAccounts()">保存账号</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量操作模态框 -->
    <div class="modal fade" id="batchOperationModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-tasks me-2"></i>批量操作
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">选择操作</label>
                        <select class="form-select" id="batchOperation">
                            <option value="">请选择操作</option>
                            <option value="status-active">批量设为活跃</option>
                            <option value="status-pending">批量设为待处理</option>
                            <option value="status-rented">批量设为已租</option>
                            <option value="status-sold">批量设为已售</option>
                            <option value="status-expired">批量设为过期</option>
                            <option value="delete">批量删除</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">选择账号</label>
                        <div id="batchAccountList" class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                            <!-- 账号列表将在这里动态生成 -->
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        已选择 <span id="selectedCount">0</span> 个账号
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="accountManager.executeBatchOperation()">执行操作</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 类型管理模态框 -->
    <div class="modal fade" id="typeManagerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-cog me-2"></i>管理账号类型
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">添加新类型</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="newTypeName" placeholder="输入新的账号类型">
                            <button class="btn btn-primary" onclick="accountManager.addAccountType()">
                                <i class="fas fa-plus"></i> 添加
                            </button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">现有类型</label>
                        <div id="existingTypes" class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                            <!-- 现有类型列表将在这里动态生成 -->
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        删除类型不会影响已有账号，但会从筛选器中移除该类型
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 平台管理模态框 -->
    <div class="modal fade" id="platformManagerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-server me-2"></i>管理平台
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">添加新平台</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="newPlatformName" placeholder="输入新的平台名称">
                            <button class="btn btn-primary" onclick="accountManager.addPlatform()">
                                <i class="fas fa-plus"></i> 添加
                            </button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">现有平台</label>
                        <div id="existingPlatforms" class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                            <!-- 现有平台列表将在这里动态生成 -->
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        删除平台不会影响已有账号，但会从筛选器中移除该平台
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>



    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 自定义脚本 -->
    <script src="js/account-data.js"></script>
    <script src="js/account-utils.js"></script>
    <script src="js/account-manager.js"></script>


</body>
</html>
