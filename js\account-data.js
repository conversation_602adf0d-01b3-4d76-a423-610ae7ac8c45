// 账号数据管理模块
class AccountDataManager {
    constructor() {
        this.storageKey = 'accountManagerData';
        this.data = this.loadData();
        this.initializeData();
    }

    // 初始化数据结构
    initializeData() {
        if (!this.data.accounts) this.data.accounts = [];
        if (!this.data.categories) {
            this.data.categories = [];
        }
        if (!this.data.platforms) {
            this.data.platforms = [];
        }
        if (!this.data.settings) {
            this.data.settings = {
                autoBackup: true,
                passwordVisible: false,
                theme: 'business',
                encryptPasswords: true
            };
        }
        if (!this.data.statistics) {
            this.data.statistics = {
                totalAccounts: 0,
                activeAccounts: 0,
                pendingAccounts: 0,
                rentedAccounts: 0,
                soldAccounts: 0,
                expiredAccounts: 0
            };
        }

        // 数据迁移：将旧的value字段转换为valueMin和valueMax
        this.migrateValueFields();

        // 添加一些测试数据（包含过期账号）
        if (this.data.accounts.length === 0) {
            this.data.accounts = [
                {
                    id: 'test1',
                    type: '游戏',
                    platform: 'Steam',
                    username: '<EMAIL>',
                    password: this.encryptPassword('password123'),
                    status: 'active',
                    valueMin: 80,
                    valueMax: 120,
                    notes: '测试活跃账号',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 'test2',
                    type: '社交',
                    platform: '微信',
                    username: 'testuser2',
                    password: this.encryptPassword('password456'),
                    status: 'expired',
                    valueMin: 40,
                    valueMax: 60,
                    notes: '测试过期账号1',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 'test3',
                    type: '工具',
                    platform: 'GitHub',
                    username: '<EMAIL>',
                    password: this.encryptPassword('password789'),
                    status: 'expired',
                    valueMin: 150,
                    valueMax: 250,
                    notes: '测试过期账号2',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 'test4',
                    type: '游戏',
                    platform: 'Epic Games',
                    username: '<EMAIL>',
                    password: this.encryptPassword('password000'),
                    status: 'pending',
                    valueMin: 75,
                    valueMax: 75,
                    notes: '测试待处理账号',
                    createdAt: new Date().toISOString()
                }
            ];

            this.data.platforms = ['Steam', '微信', 'GitHub', 'Epic Games'];
        }

        this.saveData();
    }

    // 数据迁移：将旧的value字段转换为valueMin和valueMax
    migrateValueFields() {
        let needsSave = false;

        this.data.accounts.forEach(account => {
            // 如果存在旧的value字段但没有新的valueMin/valueMax字段
            if (account.hasOwnProperty('value') && !account.hasOwnProperty('valueMin')) {
                const oldValue = parseFloat(account.value) || 0;
                account.valueMin = oldValue;
                account.valueMax = oldValue;
                delete account.value; // 删除旧字段
                needsSave = true;
            }

            // 确保新字段存在
            if (!account.hasOwnProperty('valueMin')) {
                account.valueMin = 0;
                needsSave = true;
            }
            if (!account.hasOwnProperty('valueMax')) {
                account.valueMax = 0;
                needsSave = true;
            }
        });

        if (needsSave) {
            this.saveData();
        }
    }

    // 从本地存储加载数据
    loadData() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            return stored ? JSON.parse(stored) : {};
        } catch (error) {
            console.error('加载数据失败:', error);
            return {};
        }
    }

    // 保存数据到本地存储
    saveData() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.data));
            this.updateStatistics();
        } catch (error) {
            console.error('保存数据失败:', error);
        }
    }

    // 生成唯一ID
    generateId() {
        return 'acc_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // 简单的密码加密（实际项目中应使用更安全的方法）
    encryptPassword(password) {
        if (!this.data.settings.encryptPasswords) return password;
        return btoa(password); // Base64编码，仅作演示
    }

    // 解密密码
    decryptPassword(encryptedPassword) {
        if (!this.data.settings.encryptPasswords) return encryptedPassword;
        try {
            return atob(encryptedPassword);
        } catch (error) {
            return encryptedPassword; // 如果解密失败，返回原值
        }
    }

    // 添加账号
    addAccount(accountData) {
        const account = {
            id: this.generateId(),
            type: accountData.type,
            platform: accountData.platform,
            username: accountData.username,
            password: this.encryptPassword(accountData.password),
            status: accountData.status || 'active',
            notes: accountData.notes || '',
            valueMin: parseFloat(accountData.valueMin) || 0,
            valueMax: parseFloat(accountData.valueMax) || 0,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            rentInfo: accountData.status === 'rented' ? {
                renter: accountData.renter || '',
                startDate: accountData.rentStartDate || '',
                endDate: accountData.rentEndDate || '',
                price: parseFloat(accountData.rentPrice) || 0
            } : null
        };

        this.data.accounts.push(account);

        // 添加新的账号类型到分类列表
        if (account.type && !this.data.categories.includes(account.type)) {
            this.data.categories.push(account.type);
        }

        // 添加新的平台到平台列表
        if (account.platform && !this.data.platforms.includes(account.platform)) {
            this.data.platforms.push(account.platform);
        }

        this.saveData();
        return account;
    }

    // 更新账号
    updateAccount(id, accountData) {
        const index = this.data.accounts.findIndex(acc => acc.id === id);
        if (index === -1) return null;

        const account = this.data.accounts[index];
        account.type = accountData.type;
        account.platform = accountData.platform;
        account.username = accountData.username;
        account.password = this.encryptPassword(accountData.password);
        account.status = accountData.status;
        account.notes = accountData.notes || '';
        account.valueMin = parseFloat(accountData.valueMin) || 0;
        account.valueMax = parseFloat(accountData.valueMax) || 0;
        account.updatedAt = new Date().toISOString();

        // 更新租赁信息
        if (accountData.status === 'rented') {
            account.rentInfo = {
                renter: accountData.renter || '',
                startDate: accountData.rentStartDate || '',
                endDate: accountData.rentEndDate || '',
                price: parseFloat(accountData.rentPrice) || 0
            };
        } else {
            account.rentInfo = null;
        }

        // 添加新的账号类型到分类列表
        if (account.type && !this.data.categories.includes(account.type)) {
            this.data.categories.push(account.type);
        }

        // 添加新的平台到平台列表
        if (account.platform && !this.data.platforms.includes(account.platform)) {
            this.data.platforms.push(account.platform);
        }

        this.saveData();
        return account;
    }

    // 删除账号
    deleteAccount(id) {
        const index = this.data.accounts.findIndex(acc => acc.id === id);
        if (index === -1) return false;

        this.data.accounts.splice(index, 1);
        this.saveData();
        return true;
    }

    // 获取所有账号
    getAllAccounts() {
        return this.data.accounts.map(account => ({
            ...account,
            password: this.decryptPassword(account.password)
        }));
    }

    // 根据ID获取账号
    getAccountById(id) {
        const account = this.data.accounts.find(acc => acc.id === id);
        if (!account) return null;
        
        return {
            ...account,
            password: this.decryptPassword(account.password)
        };
    }

    // 筛选账号
    filterAccounts(filters) {
        let accounts = this.getAllAccounts();

        if (filters.type && filters.type !== 'all') {
            accounts = accounts.filter(acc => acc.type === filters.type);
        }

        if (filters.platform && filters.platform !== 'all') {
            accounts = accounts.filter(acc => acc.platform === filters.platform);
        }

        if (filters.status && filters.status !== 'all') {
            accounts = accounts.filter(acc => acc.status === filters.status);
        }

        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            accounts = accounts.filter(acc =>
                acc.platform.toLowerCase().includes(searchTerm) ||
                acc.username.toLowerCase().includes(searchTerm) ||
                acc.notes.toLowerCase().includes(searchTerm) ||
                acc.type.toLowerCase().includes(searchTerm)
            );
        }

        // 按平台排序，使同一平台的账号排在一起
        accounts.sort((a, b) => {
            // 首先按平台名称排序
            const platformCompare = a.platform.localeCompare(b.platform);
            if (platformCompare !== 0) {
                return platformCompare;
            }

            // 如果平台相同，再按账号类型排序
            const typeCompare = a.type.localeCompare(b.type);
            if (typeCompare !== 0) {
                return typeCompare;
            }

            // 如果平台和类型都相同，按用户名排序
            return a.username.localeCompare(b.username);
        });

        return accounts;
    }

    // 更新统计信息
    updateStatistics() {
        const accounts = this.data.accounts;
        this.data.statistics = {
            totalAccounts: accounts.length,
            activeAccounts: accounts.filter(acc => acc.status === 'active').length,
            pendingAccounts: accounts.filter(acc => acc.status === 'pending').length,
            rentedAccounts: accounts.filter(acc => acc.status === 'rented').length,
            soldAccounts: accounts.filter(acc => acc.status === 'sold').length,
            expiredAccounts: accounts.filter(acc => acc.status === 'expired').length
        };
    }

    // 获取统计信息
    getStatistics() {
        this.updateStatistics();
        return this.data.statistics;
    }

    // 获取所有平台列表
    getAllPlatforms() {
        return [...this.data.platforms].sort();
    }

    // 添加平台
    addPlatform(platformName) {
        if (platformName && !this.data.platforms.includes(platformName)) {
            this.data.platforms.push(platformName);
            this.saveData();
            return true;
        }
        return false;
    }

    // 删除平台
    deletePlatform(platformName) {
        const index = this.data.platforms.indexOf(platformName);
        if (index > -1) {
            this.data.platforms.splice(index, 1);
            this.saveData();
            return true;
        }
        return false;
    }



    // 批量更新账号状态
    batchUpdateStatus(accountIds, newStatus) {
        let updated = 0;
        accountIds.forEach(id => {
            const account = this.data.accounts.find(acc => acc.id === id);
            if (account) {
                account.status = newStatus;
                account.updatedAt = new Date().toISOString();
                updated++;
            }
        });
        
        if (updated > 0) {
            this.saveData();
        }
        
        return updated;
    }

    // 批量删除账号
    batchDeleteAccounts(accountIds) {
        let deleted = 0;
        accountIds.forEach(id => {
            const index = this.data.accounts.findIndex(acc => acc.id === id);
            if (index !== -1) {
                this.data.accounts.splice(index, 1);
                deleted++;
            }
        });
        
        if (deleted > 0) {
            this.saveData();
        }
        
        return deleted;
    }

    // 获取设置
    getSettings() {
        return this.data.settings;
    }

    // 更新设置
    updateSettings(newSettings) {
        this.data.settings = { ...this.data.settings, ...newSettings };
        this.saveData();
    }

    // 批量添加账号
    batchAddAccounts(accountsData) {
        const results = {
            imported: 0,
            updated: 0,
            skipped: 0,
            errors: []
        };

        accountsData.forEach((accountData, index) => {
            try {
                // 检查是否已存在
                const existing = this.data.accounts.find(acc =>
                    acc.platform === accountData.platform && acc.username === accountData.username
                );

                if (existing) {
                    results.skipped++;
                } else {
                    this.addAccount(accountData);
                    results.imported++;
                }
            } catch (error) {
                results.errors.push({
                    index: index,
                    error: error.message,
                    data: accountData
                });
            }
        });

        return results;
    }

    // 检查账号是否存在
    accountExists(platform, username) {
        return this.data.accounts.some(acc =>
            acc.platform === platform && acc.username === username
        );
    }
}

// 创建全局数据管理器实例
const accountDataManager = new AccountDataManager();
